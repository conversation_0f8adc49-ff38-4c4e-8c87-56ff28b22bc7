
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";

const Contact = () => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    message: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    setTimeout(() => {
      toast({
        title: "Message sent!",
        description: "Thanks for reaching out. I'll get back to you soon.",
      });
      setFormData({ name: "", email: "", message: "" });
      setIsSubmitting(false);
    }, 1500);
  };

  return (
    <section id="contact" className="py-24">
      <div className="container px-4 md:px-6 max-w-3xl">
        <h2 className="section-heading text-center">
          <span className="text-aqua font-mono font-normal mr-2">03.</span>Get In Touch
        </h2>
        
        <p className="text-center text-slate mb-12 max-w-lg mx-auto">
          I'm currently looking for new opportunities. Whether you have a question or just want to say hi, I'll do my best to get back to you!
        </p>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div>
              <label htmlFor="name" className="block mb-2 text-sm text-light-slate">
                Your Name
              </label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="bg-light-navy border-lightest-navy focus:border-aqua text-light-slate"
                placeholder="John Doe"
              />
            </div>
            <div>
              <label htmlFor="email" className="block mb-2 text-sm text-light-slate">
                Your Email
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="bg-light-navy border-lightest-navy focus:border-aqua text-light-slate"
                placeholder="<EMAIL>"
              />
            </div>
          </div>
          <div>
            <label htmlFor="message" className="block mb-2 text-sm text-light-slate">
              Message
            </label>
            <Textarea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleChange}
              required
              className="bg-light-navy border-lightest-navy focus:border-aqua text-light-slate min-h-[150px]"
              placeholder="How can I help you?"
            />
          </div>
          <div className="text-center">
            <Button 
              type="submit"
              disabled={isSubmitting}
              className="bg-aqua/10 border border-aqua text-aqua hover:bg-aqua/20 px-8"
            >
              {isSubmitting ? "Sending..." : "Send Message"}
            </Button>
          </div>
        </form>
      </div>
    </section>
  );
};

export default Contact;
