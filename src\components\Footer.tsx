
import { Github, Linkedin } from "lucide-react";

const Footer = () => {
  return (
    <footer className="py-8 bg-navy border-t border-lightest-navy">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center">
          <div className="flex space-x-6 mb-6">
            <a 
              href="https://github.com" 
              target="_blank"
              rel="noopener noreferrer"
              className="text-light-slate hover:text-aqua transition-colors"
            >
              <Github size={20} />
            </a>
            <a 
              href="https://linkedin.com" 
              target="_blank"
              rel="noopener noreferrer"
              className="text-light-slate hover:text-aqua transition-colors"
            >
              <Linkedin size={20} />
            </a>
          </div>
          
          <div className="text-center">
            <p className="text-slate text-sm font-mono">Designed & Built with ❤️</p>
            <p className="text-slate text-xs mt-2">© {new Date().getFullYear()} All Rights Reserved</p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
