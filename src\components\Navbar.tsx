
import { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navLinks = [
    { name: 'About', url: '#about' },
    { name: 'Projects', url: '#projects' },
    { name: 'Contact', url: '#contact' },
  ];

  const handleMenuToggle = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleNavLinkClick = () => {
    setIsMenuOpen(false);
  };

  return (
    <header className={`fixed w-full z-50 transition-all duration-300 ${scrolled ? 'py-3 bg-navy/90 backdrop-blur shadow-md' : 'py-5 bg-transparent'}`}>
      <div className="container px-4 md:px-6 flex items-center justify-between">
        <a href="#" className="text-aqua text-2xl font-bold font-mono">
          {"<Portfolio />"}
        </a>

        {/* Desktop navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          {navLinks.map((link, index) => (
            <a 
              key={link.name} 
              href={link.url}
              className="nav-link"
            >
              <span className="text-aqua font-mono">{`0${index + 1}.`}</span> {link.name}
            </a>
          ))}
          <Button variant="outline" className="border-aqua text-aqua hover:bg-aqua/10">
            Resume
          </Button>
        </nav>

        {/* Mobile menu button */}
        <button 
          className="md:hidden text-light-slate hover:text-aqua transition-colors"
          onClick={handleMenuToggle}
          aria-label="Toggle menu"
        >
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile menu */}
      <div 
        className={`fixed top-0 right-0 w-3/4 h-full bg-light-navy p-6 transform transition-transform duration-300 ease-in-out z-50 ${
          isMenuOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        <div className="flex justify-end">
          <button 
            className="text-light-slate hover:text-aqua transition-colors"
            onClick={handleMenuToggle}
            aria-label="Close menu"
          >
            <X size={24} />
          </button>
        </div>
        <nav className="flex flex-col items-center mt-12 space-y-6">
          {navLinks.map((link, index) => (
            <a 
              key={link.name} 
              href={link.url}
              className="text-xl text-lightest-slate hover:text-aqua transition-colors"
              onClick={handleNavLinkClick}
            >
              <span className="text-aqua font-mono block text-center mb-1">{`0${index + 1}.`}</span>
              {link.name}
            </a>
          ))}
          <Button variant="outline" className="border-aqua text-aqua hover:bg-aqua/10 mt-4 w-full">
            Resume
          </Button>
        </nav>
      </div>
      
      {/* Overlay for mobile menu */}
      {isMenuOpen && (
        <div 
          className="fixed inset-0 bg-navy/80 backdrop-blur-sm z-40 md:hidden"
          onClick={handleMenuToggle}
        ></div>
      )}
    </header>
  );
};

export default Navbar;
