
import { Badge } from "@/components/ui/badge";
import { Github, LinkIcon } from "lucide-react";

interface ProjectCardProps {
  title: string;
  description: string;
  tags: string[];
  image: string;
  github?: string;
  liveLink?: string;
  reverse?: boolean;
  type: "software" | "ui-ux" | "both";
}

const ProjectCard = ({
  title,
  description,
  tags,
  image,
  github,
  liveLink,
  reverse = false,
  type,
}: ProjectCardProps) => {
  return (
    <div className="relative mb-16 md:mb-24">
      <div className={`relative md:grid md:grid-cols-12 md:gap-8 items-center ${reverse ? 'md:text-right' : ''}`}>
        {/* Image section - always visible on mobile, conditionally positioned on desktop */}
        <div className={`md:col-span-7 ${reverse ? 'md:col-start-6' : 'md:col-start-1'}`}>
          <div className="relative group">
            <a 
              href={liveLink || github || '#'}  
              target="_blank" 
              rel="noopener noreferrer"
              className="block"
            >
              <div className="absolute inset-0 bg-aqua/20 group-hover:bg-transparent transition-colors duration-300 z-10"></div>
              <div className="overflow-hidden h-[250px] md:h-[320px]"> {/* Reduced height */}
                <img 
                  src={image} 
                  alt={title}
                  className="w-full h-full object-cover object-center brightness-50 group-hover:brightness-75 group-hover:scale-105 transition-all duration-300" 
                />
              </div>
            </a>
          </div>
        </div>

        {/* Content section */}
        <div className={`
          md:col-span-7 md:row-start-1 p-5 md:py-4 md:px-0 bg-light-navy md:bg-transparent -mt-2 md:mt-0
          ${reverse ? 'md:col-start-1' : 'md:col-start-6'}
          relative z-20 flex flex-col justify-center
        `}>
          <div>
            <Badge className={`mb-2 text-xs ${
              type === 'software' ? 'bg-purple/20 text-purple hover:bg-purple/30' : 
              type === 'ui-ux' ? 'bg-peach/20 text-peach hover:bg-peach/30' : 
              'bg-aqua/20 text-aqua hover:bg-aqua/30'
            }`}>
              {type === 'software' ? 'Software Development' : 
               type === 'ui-ux' ? 'UI/UX Design' : 
               'Full Stack Project'}
            </Badge>
            <h3 className="text-xl md:text-2xl font-bold text-lightest-slate mb-4">{title}</h3>
            <div className="bg-light-navy rounded-md p-5 mb-4">
              <p className="text-light-slate">{description}</p>
            </div>
            
            <ul className={`flex flex-wrap gap-3 text-xs font-mono mb-6 ${reverse ? 'md:justify-end' : ''}`}>
              {tags.map((tag) => (
                <li key={tag} className="text-slate">{tag}</li>
              ))}
            </ul>
            
            <div className={`flex gap-4 ${reverse ? 'md:justify-end' : ''}`}>
              {github && (
                <a 
                  href={github} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-lightest-slate hover:text-aqua transition-colors"
                >
                  <Github size={20} />
                </a>
              )}
              {liveLink && (
                <a 
                  href={liveLink} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-lightest-slate hover:text-aqua transition-colors"
                >
                  <LinkIcon size={20} />
                </a>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;
