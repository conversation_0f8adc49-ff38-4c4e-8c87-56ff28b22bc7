
import { useState } from "react";
import ProjectCard from "./ProjectCard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

type ProjectType = "all" | "software" | "ui-ux" | "3d";

const Projects = () => {
  const [activeTab, setActiveTab] = useState<ProjectType>("all");
  
  const projects = [
    {
      id: 1,
      title: "E-Commerce Platform",
      description: "A full-stack e-commerce platform built with React, Node.js, and MongoDB. Features include user authentication, product catalog, shopping cart, and order management.",
      tags: ["React", "Node.js", "Express", "MongoDB", "Redux", "Stripe"],
      image: "https://images.unsplash.com/photo-*************-c5249f4df085?auto=format&fit=crop&q=80&w=800",
      github: "https://github.com",
      liveLink: "https://example.com",
      type: "software" as const,
    },
    {
      id: 2,
      title: "Banking Mobile App",
      description: "A UI/UX design project for a mobile banking application. The design focuses on simplicity, accessibility, and security for a seamless user experience.",
      tags: ["Figma", "Adobe XD", "Wireframing", "Prototyping", "User Testing"],
      image: "https://images.unsplash.com/photo-*************-dccba630e2f6?auto=format&fit=crop&q=80&w=800",
      liveLink: "https://behance.net",
      type: "ui-ux" as const,
    },
    {
      id: 3,
      title: "Task Management Dashboard",
      description: "A collaborative project management tool with an intuitive interface and powerful features. Built with React and a custom design system.",
      tags: ["React", "TypeScript", "Tailwind CSS", "Figma", "Firebase"],
      image: "https://images.unsplash.com/photo-*************-ce68d2c6f44d?auto=format&fit=crop&q=80&w=800",
      github: "https://github.com",
      liveLink: "https://example.com",
      type: "both" as const,
    },
    {
      id: 4,
      title: "Social Media Analytics",
      description: "A data visualization dashboard that provides insights for social media performance. Features include customizable charts and exportable reports.",
      tags: ["D3.js", "React", "Node.js", "GraphQL", "PostgreSQL"],
      image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?auto=format&fit=crop&q=80&w=800",
      github: "https://github.com",
      type: "software" as const,
    },
    {
      id: 5,
      title: "Sustainable Living App",
      description: "UI/UX design for an app that helps users live more sustainably through challenges, tips, and community features.",
      tags: ["Figma", "User Research", "Interaction Design", "UI Design"],
      image: "https://images.unsplash.com/photo-1500673922987-e212871fec22?auto=format&fit=crop&q=80&w=800",
      liveLink: "https://dribbble.com",
      type: "ui-ux" as const,
    },
  ];

  const filteredProjects = activeTab === "all" 
    ? projects 
    : projects.filter(project => project.type === activeTab || project.type === "both");

  return (
    <section id="projects" className="py-24">
      <div className="container px-4 md:px-6">
        <h2 className="section-heading">
          <span className="text-aqua font-mono font-normal mr-2">02.</span>My Projects
        </h2>

        <div className="mb-12">
          <Tabs defaultValue="all" className="flex justify-center mb-12">
            <TabsList className="bg-light-navy">
              <TabsTrigger 
                value="all" 
                onClick={() => setActiveTab("all")}
                className="data-[state=active]:bg-navy data-[state=active]:text-aqua"
              >
                All Projects
              </TabsTrigger>
              <TabsTrigger 
                value="software"
                onClick={() => setActiveTab("software")}
                className="data-[state=active]:bg-navy data-[state=active]:text-purple"
              >
                Software
              </TabsTrigger>
              <TabsTrigger 
                value="ui-ux"
                onClick={() => setActiveTab("ui-ux")}
                className="data-[state=active]:bg-navy data-[state=active]:text-peach"
              >
                UI/UX Design
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <div>
            {filteredProjects.map((project, index) => (
              <ProjectCard
                key={project.id}
                title={project.title}
                description={project.description}
                tags={project.tags}
                image={project.image}
                github={project.github}
                liveLink={project.liveLink}
                reverse={index % 2 !== 0}
                type={project.type}
              />
            ))}
          </div>

          <div className="text-center mt-16">
            <Button variant="outline" className="border-aqua text-aqua hover:bg-aqua/10 font-mono">
              View More Projects
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Projects;
