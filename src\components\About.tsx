
import { Badge } from "@/components/ui/badge";

const About = () => {
  const skills = [
    "JavaScript", "TypeScript", "React", "Node.js", 
    "Angular", "Next.js", "Tailwind CSS", "Three.js", 
    "Figma", "UI/UX Design", "Blender", "CSS3", "HTML5",
    "Git", "Supabase"
  ];

  return (
    <section id="about" className="py-24">
      <div className="container px-4 md:px-6">
        <h2 className="section-heading">
          <span className="text-aqua font-mono font-normal mr-2">01.</span>About Me
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
          <div>
            <p className="mb-4">
              Hello! My name is <PERSON><PERSON> and I enjoy creating things. 
              My interest started when I was able to fix my first computer, just diagnosing problems and maintenance. Until my curiosity and eagerness to become a creator led me to become a web developer, 3D Artist, and UI/UX Designer. In the future, I am hoping to leverage my curiosity and ability to learn more skills, tools, tech stacks, and many more!
            </p>
            <p>
              Here are a few tools and tech stacks I've been working with recently:
            </p>
            
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 mt-6">
              {skills.map((skill) => (
                <div key={skill} className="flex items-center mb-2">
                  <span className="text-aqua mr-2">▹</span>
                  <span className="font-mono text-sm">{skill}</span>
                </div>
              ))}
            </div>
          </div>
          
          <div className="flex justify-center">
            <div className="relative group">
              <div className="absolute -inset-4 rounded-lg bg-aqua/20 opacity-75 group-hover:opacity-100 transition duration-300 blur-sm"></div>
              <div className="relative aspect-square w-60 sm:w-72 md:w-full max-w-xs rounded-lg overflow-hidden">
                <div className="absolute inset-0 bg-aqua/20 group-hover:bg-transparent transition duration-300 z-10"></div>
                <img 
                  src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?auto=format&fit=crop&q=80&w=400"
                  alt="Developer Portrait"
                  className="w-full h-full object-cover grayscale group-hover:grayscale-0 transition duration-300"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
