
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 33% 99%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 222 47% 11%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 210 40% 90%;
    --accent-foreground: 222 47% 11%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 222 84% 5%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5% 26%;
    --sidebar-primary: 240 6% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 5% 96%;
    --sidebar-accent-foreground: 240 6% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 91% 60%;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222 47% 11%;

    --secondary: 217 33% 18%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 18%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 33% 18%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 18%;
    --input: 217 33% 18%;
    --ring: 213 27% 84%;
  }
}

/* Base theme - Dark navy theme with aqua accent */
@layer base {
  body {
    @apply bg-navy text-light-slate;
    font-feature-settings: "rlig" 1, "calt" 1;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Plain White Theme */
  body.theme-plain {
    --navy: #ffffff;
    --light-navy: #f5f5f5; 
    --lightest-navy: #eeeeee;
    --slate: #555555;
    --light-slate: #333333;
    --lightest-slate: #111111;
    --aqua: #0366d6;
    --purple: #5a67d8;
    --purple-rgb: 90, 103, 216;
    --peach: #f97316;
  }

  /* Cool Colors Theme for Indoor Use */
  body.theme-cool {
    --navy: #1a202c;
    --light-navy: #2d3748; 
    --lightest-navy: #4a5568;
    --slate: #a0aec0;
    --light-slate: #cbd5e0;
    --lightest-slate: #e2e8f0;
    --aqua: #81e6d9;
    --purple: #9b87f5;
    --purple-rgb: 155, 135, 245;
    --peach: #c1b3ff;
  }

  /* Bright Colors Theme for Outdoor Visibility */
  body.theme-bright {
    --navy: #000000;
    --light-navy: #1a1a1a; 
    --lightest-navy: #2c2c2c;
    --slate: #d1d1d1;
    --light-slate: #f0f0f0;
    --lightest-slate: #ffffff;
    --aqua: #00ffcc;
    --purple: #c576f6;
    --purple-rgb: 197, 118, 246;
    --peach: #ff9e64;
  }

  * {
    @apply border-border;
  }

  html {
    @apply scroll-smooth;
  }

  ::selection {
    background-color: rgba(var(--purple-rgb), 0.3);
    @apply text-white;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-navy;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-lightest-navy rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-light-slate;
  }
}

@layer components {
  .section-heading {
    @apply text-2xl md:text-3xl font-bold text-lightest-slate relative
      after:content-[''] after:block after:w-full after:max-w-[100px] 
      after:h-[1px] after:bg-lightest-navy after:mt-2 mb-8 md:mb-12;
  }

  .project-card-link {
    @apply relative inline-block after:content-[''] after:absolute after:w-full 
      after:scale-x-0 after:h-0.5 after:bottom-0 after:left-0 after:bg-aqua
      after:origin-bottom-right after:transition-transform after:duration-300
      hover:after:scale-x-100 hover:after:origin-bottom-left;
  }

  .nav-link {
    @apply relative inline-block text-light-slate hover:text-aqua transition-colors
      after:content-[''] after:absolute after:w-0 after:h-[2px] after:bg-aqua 
      after:left-0 after:bottom-[-4px] after:transition-all after:duration-300
      hover:after:w-full;
  }
}
