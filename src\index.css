
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 33% 99%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 222 47% 11%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 210 40% 90%;
    --accent-foreground: 222 47% 11%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 222 84% 5%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5% 26%;
    --sidebar-primary: 240 6% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 5% 96%;
    --sidebar-accent-foreground: 240 6% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 91% 60%;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222 47% 11%;

    --secondary: 217 33% 18%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 18%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 33% 18%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 18%;
    --input: 217 33% 18%;
    --ring: 213 27% 84%;
  }
}

/* Base theme - Dark navy theme with aqua accent */
@layer base {
  body {
    @apply bg-navy text-light-slate;
    font-feature-settings: "rlig" 1, "calt" 1;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Plain White Theme (Clean & Subtle) */
body.theme-plain {
  --navy: #FFFFFF;      /* Pure White */
  --light-navy: #F9F9F9; /* Off-White, very subtle */
  --lightest-navy: #DCDCDC; /* Very Light Gray */
  --slate: #777777;     /* Medium Gray for body text */
  --light-slate: #333333; /* Dark Gray for headings */
  --lightest-slate: #1A1A1A; /* Near Black for strong emphasis */
  --aqua: #607D8B;      /* Muted Blue-Grey Accent */
  --purple: #78909C;    /* Cooler Blue-Grey Accent */
  --purple-rgb: 120, 144, 156; /* RGB for the cooler blue-grey */
  --peach: #B0BEC5;     /* Even lighter blue-grey for subtle highlights */

  /* Override button/UI component colors */
  --background: 255 255 255; /* White */
  --foreground: 26 26 26; /* Near Black */
  --primary: 96 125 139; /* Muted Blue-Grey */
  --primary-foreground: 255 255 255; /* White */
  --secondary: 249 249 249; /* Off-White */
  --secondary-foreground: 26 26 26; /* Near Black */
  --accent: 220 220 220; /* Very Light Gray */
  --accent-foreground: 26 26 26; /* Near Black */
  --muted: 249 249 249; /* Off-White */
  --muted-foreground: 119 119 119; /* Medium Gray */
}

/* Cool Colors Theme for Indoor Use (Refined & Harmonious) */
body.theme-cool {
  --navy: #1E2A38;         /* Deep Desaturated Navy - anchor color */
  --light-navy: #2E3C4F;   /* Muted Blue-Gray - base for surfaces */
  --lightest-navy: #4C5C68; /* Cool Slate Blue - tertiary sections */

  --slate: #F1F4F6;        /* Soft Cool White - background contrast */
  --light-slate: #DDE4EA;  /* Muted Light Gray-Blue - secondary bg */
  --lightest-slate: #BCCAD6; /* Subtle Bluish Gray - for lines/dividers */

  --aqua: #7FD8BE;         /* Soft Aquamarine - subtle accent */
  --purple: #A29BFE;       /* Lavender Blue - complementary cool tone */
  --purple-rgb: 162, 155, 254; /* RGB for soft purple accents */

  --peach: #FFE0B2;        /* Muted Warm Neutral - warm contrast (optional) */

  /* Override button/UI component colors to align with cool theme */
  --background: 30 42 56; /* Deep Desaturated Navy */
  --foreground: 241 244 246; /* Soft Cool White */
  --primary: 127 216 190; /* Soft Aquamarine */
  --primary-foreground: 30 42 56; /* Deep Navy */
  --secondary: 46 60 79; /* Muted Blue-Gray */
  --secondary-foreground: 241 244 246; /* Soft Cool White */
  --accent: 76 92 104; /* Cool Slate Blue */
  --accent-foreground: 241 244 246; /* Soft Cool White */
  --muted: 46 60 79; /* Muted Blue-Gray */
  --muted-foreground: 188 202 214; /* Subtle Bluish Gray */
}


  * {
    @apply border-border;
  }

  html {
    @apply scroll-smooth;
  }

  ::selection {
    background-color: rgba(var(--purple-rgb), 0.3);
    @apply text-white;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-navy;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-lightest-navy rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-light-slate;
  }
}

@layer components {
  .section-heading {
    @apply text-2xl md:text-3xl font-bold text-lightest-slate relative
      after:content-[''] after:block after:w-full after:max-w-[100px]
      after:h-[1px] after:bg-lightest-navy after:mt-2 mb-8 md:mb-12;
  }

  .project-card-link {
    @apply relative inline-block after:content-[''] after:absolute after:w-full
      after:scale-x-0 after:h-0.5 after:bottom-0 after:left-0 after:bg-aqua
      after:origin-bottom-right after:transition-transform after:duration-300
      hover:after:scale-x-100 hover:after:origin-bottom-left;
  }

  .nav-link {
    @apply relative inline-block text-light-slate hover:text-aqua transition-colors
      after:content-[''] after:absolute after:w-0 after:h-[2px] after:bg-aqua
      after:left-0 after:bottom-[-4px] after:transition-all after:duration-300
      hover:after:w-full;
  }
}
