
import { Button } from "@/components/ui/button";

const Hero = () => {
  return (
    <section id="hero" className="min-h-[100vh] flex items-center pt-16 pb-12">
      <div className="container px-4 md:px-6">
        <div className="animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
          <p className="text-aqua font-mono mb-5">Hi, my name is</p>
        </div>
        <div className="animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-lightest-slate mb-4">
            Arian.
          </h1>
        </div>
        <div className="animate-fade-in-up max-w-lg" style={{ animationDelay: '0.8s' }}>
          <p className="text-slate text-lg mb-8">
            I'm a web developer, and an amateur 3D artist. I make things that I enjoy doing. I also practice Agile Scrum methodology with experience as a Scrum Master and Team Project Management.
          </p>
        </div>
        <div className="animate-fade-in-up" style={{ animationDelay: '1s' }}>
          <Button 
            size="lg"
            variant="outline" 
            className="border-aqua text-aqua hover:bg-aqua/10 font-mono"
            asChild
          >
            <a href="#projects">Check out my work!</a>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default Hero;
