
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { useTheme } from "@/contexts/ThemeContext";
import { Badge } from "@/components/ui/badge";

const ColorSchemeToggle = () => {
  const { colorScheme, setColorScheme } = useTheme();

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <div className="bg-lightest-navy/80 backdrop-blur-sm p-3 rounded-xl shadow-lg border border-lightest-navy">
        <div className="mb-2 text-xs text-center font-mono text-aqua">
          <span>Color Mode</span>
        </div>
        <ToggleGroup type="single" value={colorScheme} onValueChange={(value) => value && setColorScheme(value as "plain" | "cool")}>
          <ToggleGroupItem value="plain" aria-label="Plain white theme" className="px-3 data-[state=on]:bg-black data-[state=on]:text-navy">
            <span className="text-xs">Plain</span>
          </ToggleGroupItem>
          <ToggleGroupItem value="cool" aria-label="Cool colors theme" className="px-3 data-[state=on]:bg-purple data-[state=on]:text-navy">
            <span className="text-xs">Cool</span>
          </ToggleGroupItem>
        </ToggleGroup>
      </div>
    </div>
  );
};

export default ColorSchemeToggle;
