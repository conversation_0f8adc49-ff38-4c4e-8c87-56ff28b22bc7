
import React, { createContext, useContext, useState, useEffect } from "react";

type ColorScheme = "plain" | "cool";

type ThemeContextType = {
  colorScheme: ColorScheme;
  setColorScheme: (scheme: ColorScheme) => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  const [colorScheme, setColorScheme] = useState<ColorScheme>(() => {
    // Try to get the saved theme from localStorage
    const savedScheme = localStorage.getItem("colorScheme");
    return (savedScheme as ColorScheme) || "plain";
  });

  // Save theme to localStorage when it changes
  useEffect(() => {
    localStorage.setItem("colorScheme", colorScheme);

    // Apply appropriate class to the document body
    document.body.classList.remove("theme-plain", "theme-cool");
    document.body.classList.add(`theme-${colorScheme}`);
  }, [colorScheme]);

  return (
    <ThemeContext.Provider value={{ colorScheme, setColorScheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};
